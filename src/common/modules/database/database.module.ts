import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { Branch } from './entities/branch.entity';
import { UserBranch } from './entities/user-branch.entity';
import { Role } from './entities/role.entity';
import { RolePermission } from './entities/role-permission.entity';
import { Module as ModuleEntity } from './entities/module.entity';
import { Timezone } from './entities/timezone.entity';
import { Label } from './entities/label.entity';
import { LogUserDevice } from './entities/log-user-device.entity';
import { Device } from './entities/device.entity';
import { Scheduler } from './entities/scheduler.entity';
import { Frequency } from './entities/frequency.entity';
import { Checkpoint } from './entities/checkpoint.entity';
import { LogCheckpointBatteryLevel } from './entities/log-checkpoint-battery-levels.entity';
import { LogAlarm } from './entities/log-alarm.entity';
import { LogGeolocation } from './entities/log-geolocation.entity';
import { LogForm } from './entities/log-form.entity';
import { LogTask } from './entities/log-task.entity';
import { SchedulerRecipient } from './entities/scheduler-recipient.entity';
import { SchedulerCheckpointLabel } from './entities/scheduler-checkpoint-label.entity';
import { ReportType } from './entities/report-type.entity';
import { FormPicklist } from './entities/form-picklist.entity';
import { AuthAccessToken } from './entities/auth-access-token.entity';
import { LogAlert } from './entities/log-alert.entity';
import { LogFormField } from './entities/log-form-field.entity';
import { LogTaskField } from './entities/log-task-field.entity';
import { LogActivity } from './entities/log-activity.entity';
import { SchedulerZoneLabel } from './entities/scheduler-zone-label.entity';
import { SchedulerUserLabel } from './entities/scheduler-user-label.entity';
import { SchedulerDeviceLabel } from './entities/scheduler-device-label.entity';
import { ZoneLabel } from './entities/zone-label.entity';
import { ZoneRecipient } from './entities/zone-recipient.entity';
import { Zone } from './entities/zone.entity';
import { UserLabel } from './entities/user-label.entity';
import { TaskField } from './entities/task-field.entity';
import { Task } from './entities/task.entity';
import { UniguardDeviceType } from './entities/uniguard-device-type.entity';
import { Setting } from './entities/setting.entity';
import { Reseller } from './entities/reseller.entity';
import { Notification } from './entities/notification.entity';
import { LogCheckpoint } from './entities/log-checkpoint.entity';
import { LogUnknownCheckpoint } from './entities/log-unknown-checkpoint.entity';
import { License } from './entities/license.entity';
import { GeofenceLabel } from './entities/geofence-label.entity';
import { Geofence } from './entities/geofence.entity';
import { Form } from './entities/form.entity';
import { FormPicklistOption } from './entities/form-picklist-option.entity';
import { Event } from './entities/event.entity';
import { FieldType } from './entities/field-type.entity';
import { FormBranch } from './entities/form-branch.entity';
import { FormField } from './entities/form-field.entity';
import { DeviceLabel } from './entities/device-label.entity';
import { CheckpointLabel } from './entities/checkpoint-label.entity';
import { CheckpointType } from './entities/checkpoint-type.entity';
import { Beacon } from './entities/beacon.entity';
import { BranchLabel } from './entities/branch-label.entity';
import { Alert } from './entities/alert.entity';
import { AlertRecipient } from './entities/alert-recipient.entity';
import { AlertEvent } from './entities/alert-event.entity';
import { AlertCondition } from './entities/alert-condition.entity';
import { AlertConditionType } from './entities/alert-condition-type.entity';
import { AlertBranch } from './entities/alert-branch.entity';
import { AlertAction } from './entities/alert-action.entity';
import { Activity } from './entities/activity.entity';

@Global()
@Module({
  imports: [
    // Register all entities here for easier access across the application
    TypeOrmModule.forFeature([
      Activity,
      Alert,
      AlertAction,
      AlertBranch,
      AlertCondition,
      AlertConditionType,
      AlertEvent,
      AlertRecipient,
      AuthAccessToken,
      Beacon,
      Branch,
      BranchLabel,
      Checkpoint,
      CheckpointLabel,
      CheckpointType,
      Device,
      DeviceLabel,
      Event,
      FieldType,
      Form,
      FormBranch,
      FormField,
      FormPicklist,
      FormPicklistOption,
      Frequency,
      Geofence,
      GeofenceLabel,
      Label,
      License,
      LogActivity,
      LogAlert,
      LogAlarm,
      LogCheckpoint,
      LogCheckpointBatteryLevel,
      LogForm,
      LogFormField,
      LogGeolocation,
      LogTask,
      LogTaskField,
      LogUnknownCheckpoint,
      LogUserDevice,
      ModuleEntity,
      Notification,
      ReportType,
      Reseller,
      Role,
      RolePermission,
      Scheduler,
      SchedulerCheckpointLabel,
      SchedulerDeviceLabel,
      SchedulerRecipient,
      SchedulerUserLabel,
      SchedulerZoneLabel,
      Setting,
      Task,
      TaskField,
      Timezone,
      UniguardDeviceType,
      User,
      UserBranch,
      UserLabel,
      Zone,
      ZoneLabel,
      ZoneRecipient,
    ]),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {}
