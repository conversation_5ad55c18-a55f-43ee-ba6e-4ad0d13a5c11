import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Branch } from './branch.entity';
import { Role } from './role.entity';
import { User } from './user.entity';
import { Device } from './device.entity';
import { Timezone } from './timezone.entity';
import { CheckpointType } from './checkpoint-type.entity';

@Entity({ name: 'log_unknown_checkpoints', schema: 'public' })
export class LogUnknownCheckpoint {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ type: 'uuid', nullable: true })
  uuid: string;

  @Column({ name: 'parent_branch_id', type: 'bigint', nullable: true })
  parent_branch_id: number;

  @ManyToOne(() => Branch)
  @JoinColumn({ name: 'parent_branch_id' })
  @Index('idx_log_unknown_checkpoints_parent_branch')
  parent_branch: Branch;

  @Column({ name: 'role_id', type: 'bigint', nullable: true })
  role_id: number;

  @ManyToOne(() => Role)
  @JoinColumn({ name: 'role_id' })
  @Index('idx_log_unknown_checkpoints_role')
  role: Role;

  @Column({ name: 'role_name', nullable: true })
  role_name: string;

  @Column({ name: 'user_id', type: 'bigint', nullable: true })
  user_id: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  @Index('idx_log_unknown_checkpoints_user')
  user: User;

  @Column({ name: 'user_name', nullable: true })
  user_name: string;

  @Column({ name: 'device_id', type: 'bigint', nullable: true })
  device_id: number;

  @ManyToOne(() => Device)
  @JoinColumn({ name: 'device_id' })
  @Index('idx_log_unknown_checkpoints_device')
  device: Device;

  @Column({ name: 'device_name', nullable: true })
  device_name: string;

  @Column({ name: 'timezone_id', type: 'bigint', nullable: true })
  timezone_id: number;

  @ManyToOne(() => Timezone)
  @JoinColumn({ name: 'timezone_id' })
  @Index('idx_log_unknown_checkpoints_timezone')
  timezone: Timezone;

  @Column({ name: 'timezone_name', nullable: true })
  timezone_name: string;

  @Column({ type: 'double precision', nullable: true })
  latitude: number;

  @Column({ type: 'double precision', nullable: true })
  longitude: number;

  @Column({ name: 'major_value', type: 'bigint', nullable: true })
  major_value: number;

  @Column({ name: 'minor_value', type: 'bigint', nullable: true })
  minor_value: number;

  @Column({ name: 'checkpoint_type_id', type: 'bigint', nullable: true })
  checkpoint_type_id: number;

  @ManyToOne(() => CheckpointType)
  @JoinColumn({ name: 'checkpoint_type_id' })
  @Index('idx_log_unknown_checkpoints_checkpoint_type')
  checkpoint_type: CheckpointType;

  @Column({ name: 'serial_number', nullable: true })
  serial_number: string;

  @Column({
    name: 'original_submitted_time',
    type: 'timestamp with time zone',
    nullable: true,
  })
  original_submitted_time: Date;

  @CreateDateColumn({
    name: 'event_time',
    type: 'timestamp with time zone',
    default: () => 'CURRENT_TIMESTAMP',
    nullable: false,
  })
  event_time: Date;
}
